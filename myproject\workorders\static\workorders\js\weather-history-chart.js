/**
 * Weather History Chart for Workorders
 * Based on the existing weather chart but adapted for historical data display
 */

// Constants
const CHART_CONSTANTS = {
    COLORS: {
        TEMPERATURE: '#FF6B6B',
        WIND_SPEED: '#10B981',
        WIND_GUSTS: '#8B5CF6',
        PRECIPITATION: '#3B82F6',
        NIGHT_OVERLAY: 'rgba(0, 0, 0, 0.1)',
        ZOOM_SELECTION: 'rgba(48, 162, 214, 0.2)',
        ZOOM_BORDER: 'rgba(55, 162, 214, 0.8)',
        GRID: '#e0e0e0',
        TEXT: '#666',
        RESET_BUTTON: '#517aee',
        RESET_BUTTON_HOVER: '#3b82f6'
    },
    DIMENSIONS: {
        CHART_HEIGHT: '320px',
        LEGEND_FONT_SIZE: 11,
        BUTTON_PADDING: '4px 8px'
    },
    TIMING: {
        CHART_CREATION_DELAY: 10,
        BUTTON_POSITION_DELAY: 50,
        RENDER_DELAY: 100
    },
    NIGHT_HOURS: {
        START: 23,
        END: 7
    }
};

// Night hours plugin for Chart.js (reused from weather-chart.js)
const nightHoursPlugin = {
    id: 'nightHours',
    beforeDraw: (chart) => {
        const { ctx, chartArea, scales } = chart;

        if (!chartArea || !scales.x || !chart.data.datasets[0].nightHours) {
            return;
        }

        const nightHours = chart.data.datasets[0].nightHours;
        const xScale = scales.x;
        const labels = chart.data.labels;

        ctx.save();
        ctx.fillStyle = CHART_CONSTANTS.COLORS.NIGHT_OVERLAY;

        for (let i = 0; i < nightHours.length; i++) {
            if (nightHours[i]) {
                const xStart = xScale.getPixelForValue(labels[i]);
                const xEnd = i < nightHours.length - 1 ? xScale.getPixelForValue(labels[i + 1]) : chartArea.right;

                ctx.fillRect(xStart, chartArea.top, xEnd - xStart, chartArea.bottom - chartArea.top);
            }
        }

        ctx.restore();
    }
};

// Register the plugin with Chart.js
function registerChartPlugins() {
    if (typeof Chart !== 'undefined') {
        Chart.register(nightHoursPlugin);
        // Register zoom plugin if available
        if (typeof ChartZoom !== 'undefined') {
            Chart.register(ChartZoom);
        }
    }
}

// Register plugins when Chart.js is available
if (typeof Chart !== 'undefined') {
    registerChartPlugins();
} else {
    // Wait for Chart.js to load
    document.addEventListener('DOMContentLoaded', function() {
        if (typeof Chart !== 'undefined') {
            registerChartPlugins();
        }
    });
}

class WeatherHistoryChart {
    static chartInstances = new Map();
    static resizeHandlers = new Map();

    /**
     * Render weather history chart with Chart.js
     * @param {string} containerId - ID of the container element
     * @param {Object} weatherData - Weather history data from API
     */
    static render(containerId, weatherData) {
        if (!weatherData || !weatherData.hourly) {
            return;
        }

        const canvas = document.getElementById(containerId);
        if (!canvas) {
            return;
        }

        // Destroy existing chart if it exists
        WeatherHistoryChart.destroy(containerId);

        const hourlyData = weatherData.hourly;

        // Prepare chart data
        const chartData = WeatherHistoryChart.prepareChartData(hourlyData);
        const ctx = canvas.getContext('2d');

        // Calculate scale ranges
        const scaleRanges = WeatherHistoryChart.calculateScaleRanges(chartData);

        // Create chart configuration
        const chartConfig = WeatherHistoryChart.createChartConfig(chartData, scaleRanges, weatherData);

        // Setup canvas styling
        WeatherHistoryChart.setupCanvas(canvas);

        // Create the chart
        const chartInstance = WeatherHistoryChart.createChart(ctx, chartConfig, containerId);
        if (chartInstance) {
            WeatherHistoryChart.chartInstances.set(containerId, chartInstance);

            // Create chart elements with delay
            setTimeout(() => {
                WeatherHistoryChart.createChartElements(containerId);
            }, CHART_CONSTANTS.TIMING.CHART_CREATION_DELAY);
        }
    }

    /**
     * Prepare chart data from hourly weather data
     */
    static prepareChartData(hourlyData) {
        const times = hourlyData.map(item => new Date(item.time));
        const localTimes = hourlyData.map(item => item.localTime);
        const rigaTimes = hourlyData.map(item => item.rigaTime);
        const temperatures = hourlyData.map(item => item.temperature);
        const precipitation = hourlyData.map(item => item.precipitation);
        const windSpeeds = hourlyData.map(item => item.windSpeed);
        const windGusts = hourlyData.map(item => item.windGusts);

        // Calculate night hours based on local timezone
        const nightHours = hourlyData.map(item => {
            const hourStr = item.localTime.split(' ')[1];
            const hour = parseInt(hourStr.split(':')[0]);
            return (hour >= CHART_CONSTANTS.NIGHT_HOURS.START || hour < CHART_CONSTANTS.NIGHT_HOURS.END);
        });

        return {
            times,
            localTimes,
            rigaTimes,
            temperatures,
            precipitation,
            windSpeeds,
            windGusts,
            nightHours
        };
    }

    /**
     * Calculate scale ranges for temperature, precipitation, and wind data
     */
    static calculateScaleRanges(chartData) {
        const { temperatures, precipitation, windSpeeds, windGusts } = chartData;

        const validTemperatures = temperatures.filter(t => t !== null && t !== undefined && !isNaN(t));
        const validPrecipitation = precipitation.filter(p => p !== null && p !== undefined && !isNaN(p));
        const validWindSpeeds = windSpeeds.filter(w => w !== null && w !== undefined && !isNaN(w));
        const validWindGusts = windGusts.filter(w => w !== null && w !== undefined && !isNaN(w));

        const tempMin = validTemperatures.length > 0 ? Math.min(...validTemperatures) : 0;
        const tempMax = validTemperatures.length > 0 ? Math.max(...validTemperatures) : 30;
        const precipMin = 0; // Precipitation always starts at 0
        const precipMax = validPrecipitation.length > 0 ? Math.max(...validPrecipitation) : 5;
        const windMin = [...validWindSpeeds, ...validWindGusts].length > 0 ? Math.min(...validWindSpeeds, ...validWindGusts) : 0;
        const windMax = [...validWindSpeeds, ...validWindGusts].length > 0 ? Math.max(...validWindSpeeds, ...validWindGusts) : 10;

        // Combine temperature and wind data for a single axis
        const combinedMin = Math.min(tempMin, windMin);
        const combinedMax = Math.max(tempMax, windMax);

        return {
            combined: WeatherHistoryChart.calculateScaleRange(combinedMin, combinedMax),
            precipitation: WeatherHistoryChart.calculateScaleRange(precipMin, precipMax)
        };
    }

    /**
     * Setup canvas styling
     */
    static setupCanvas(canvas) {
        canvas.style.width = '100%';
        canvas.style.height = CHART_CONSTANTS.DIMENSIONS.CHART_HEIGHT;
        canvas.style.display = 'block';
        canvas.style.visibility = 'visible';
        canvas.style.opacity = '1';
        canvas.style.position = 'relative';
        canvas.style.zIndex = '1';
    }

    /**
     * Create chart instance with error handling
     */
    static createChart(ctx, chartConfig) {
        try {
            const chartInstance = new Chart(ctx, chartConfig);
            chartInstance.update('none');
            return chartInstance;
        } catch (error) {
            return null;
        }
    }

    /**
     * Create chart configuration object
     */
    static createChartConfig(chartData, scaleRanges, weatherData) {
        const { times, localTimes, rigaTimes, temperatures, precipitation, windSpeeds, windGusts, nightHours } = chartData;

        return {
            type: 'line',
            data: {
                labels: times,
                datasets: [
                    {
                        label: 'Temperature (°C)',
                        data: temperatures,
                        borderColor: CHART_CONSTANTS.COLORS.TEMPERATURE,
                        backgroundColor: CHART_CONSTANTS.COLORS.TEMPERATURE + '1A',
                        borderWidth: 2,
                        fill: false,
                        yAxisID: 'y',
                        nightHours: nightHours,
                        pointRadius: 0,
                        pointHoverRadius: 4
                    },
                    {
                        label: 'Precipitation (mm/h)',
                        data: precipitation,
                        borderColor: CHART_CONSTANTS.COLORS.PRECIPITATION,
                        backgroundColor: CHART_CONSTANTS.COLORS.PRECIPITATION + '1A',
                        borderWidth: 2,
                        fill: false,
                        yAxisID: 'y2',
                        pointRadius: 0,
                        pointHoverRadius: 4
                    },
                    {
                        label: 'Wind Speed 80m (m/s)',
                        data: windSpeeds,
                        borderColor: CHART_CONSTANTS.COLORS.WIND_SPEED,
                        backgroundColor: CHART_CONSTANTS.COLORS.WIND_SPEED + '1A',
                        borderWidth: 2,
                        fill: false,
                        yAxisID: 'y',
                        pointRadius: 0,
                        pointHoverRadius: 4
                    },
                    {
                        label: 'Wind Gusts 80m (m/s)',
                        data: windGusts,
                        borderColor: CHART_CONSTANTS.COLORS.WIND_GUSTS,
                        backgroundColor: CHART_CONSTANTS.COLORS.WIND_GUSTS + '1A',
                        borderWidth: 2,
                        fill: false,
                        yAxisID: 'y',
                        pointRadius: 0,
                        pointHoverRadius: 4,
                        borderDash: [5, 5]
                    }
                ]
            },
            options: WeatherHistoryChart.createChartOptions(scaleRanges, weatherData, localTimes, rigaTimes)
        };
    }

    /**
     * Create chart options configuration
     */
    static createChartOptions(scaleRanges, weatherData, localTimes, rigaTimes) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            animation: false,
            interaction: {
                mode: 'index',
                intersect: false,
            },
            plugins: {
                legend: WeatherHistoryChart.createLegendConfig(),
                tooltip: WeatherHistoryChart.createTooltipConfig(weatherData, localTimes, rigaTimes),
                zoom: WeatherHistoryChart.createZoomConfig()
            },
            scales: WeatherHistoryChart.createScalesConfig(scaleRanges, weatherData)
        };
    }

    /**
     * Create legend configuration
     */
    static createLegendConfig() {
        return {
            display: true,
            position: 'bottom',
            align: 'center',
            onClick: function(event, legendItem, legend) {
                // Ignore clicks on night hours legend item
                if (legendItem.datasetIndex === -1) {
                    return;
                }
                // Use default behavior for other legend items
                Chart.defaults.plugins.legend.onClick.call(this, event, legendItem, legend);
            },
            labels: {
                usePointStyle: false,
                font: { size: CHART_CONSTANTS.DIMENSIONS.LEGEND_FONT_SIZE },
                color: CHART_CONSTANTS.COLORS.TEXT,
                padding: 15,
                boxWidth: 15,
                boxHeight: 2,
                generateLabels: function(chart) {
                    const original = chart.constructor.defaults.plugins.legend.labels.generateLabels;
                    const labels = original.call(this, chart);

                    labels.forEach(label => {
                        label.pointStyle = 'line';
                    });

                    labels.push({
                        text: 'Night Hours (23:00-07:00)',
                        fillStyle: CHART_CONSTANTS.COLORS.NIGHT_OVERLAY,
                        strokeStyle: CHART_CONSTANTS.COLORS.NIGHT_OVERLAY,
                        lineWidth: 1,
                        pointStyle: 'rect',
                        hidden: false,
                        // Add required properties to prevent errors
                        datasetIndex: -1, // Indicate this is not associated with a dataset
                        index: -1
                    });

                    return labels;
                }
            }
        };
    }

    /**
     * Create tooltip configuration
     */
    static createTooltipConfig(weatherData, localTimes, rigaTimes) {
        return {
            mode: 'index',
            intersect: false,
            position: 'nearest',
            callbacks: {
                title: function(context) {
                    const index = context[0].dataIndex;
                    const localTimezoneName = weatherData.location?.timezone || 'Local';
                    return [
                        `Europe/Riga: ${rigaTimes[index]}`,
                        `${localTimezoneName}: ${localTimes[index]}`
                    ];
                },
                label: function(context) {
                    const value = context.parsed.y;
                    if (value === null) return null;

                    const label = context.dataset.label;
                    return `${label}: ${value.toFixed(1)}`;
                }
            },
            titleColor: '#333',
            bodyColor: CHART_CONSTANTS.COLORS.TEXT,
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: '#ddd',
            borderWidth: 1,
            cornerRadius: 6,
            displayColors: true
        };
    }

    /**
     * Create zoom configuration
     */
    static createZoomConfig() {
        return {
            limits: {
                x: {min: 'original', max: 'original'}
            },
            pan: {
                enabled: true,
                mode: 'x'
            },
            zoom: {
                wheel: { enabled: true },
                pinch: { enabled: true },
                drag: {
                    enabled: true,
                    backgroundColor: CHART_CONSTANTS.COLORS.ZOOM_SELECTION,
                    borderColor: CHART_CONSTANTS.COLORS.ZOOM_BORDER,
                    borderWidth: 2,
                },
                mode: 'x',
            }
        };
    }

    /**
     * Create scales configuration
     */
    static createScalesConfig(scaleRanges, weatherData) {
        return {
            x: {
                type: 'time',
                position: 'bottom',
                time: {
                    unit: 'hour',
                    stepSize: 6,
                    displayFormats: {
                        hour: 'MM/dd HH:mm',
                        day: 'MM/dd'
                    },
                    tooltipFormat: 'MMM dd, HH:mm'
                },
                title: {
                    display: true,
                    text: weatherData.location?.timezone || 'Local Time',
                    color: CHART_CONSTANTS.COLORS.TEXT,
                    font: {
                        size: CHART_CONSTANTS.DIMENSIONS.LEGEND_FONT_SIZE,
                        weight: 'bold'
                    }
                },
                grid: { color: CHART_CONSTANTS.COLORS.GRID },
                ticks: {
                    color: CHART_CONSTANTS.COLORS.TEXT,
                    maxTicksLimit: 8,
                    callback: function(value) {
                        const date = new Date(value);
                        return new Intl.DateTimeFormat('en-US', {
                            timeZone: weatherData.location?.timezone || 'UTC',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        }).format(date);
                    }
                }
            },
            'x-riga': {
                type: 'time',
                position: 'top',
                time: {
                    unit: 'hour',
                    stepSize: 6,
                    displayFormats: {
                        hour: 'MM/dd HH:mm',
                        day: 'MM/dd'
                    }
                },
                title: {
                    display: true,
                    text: 'Europe/Riga',
                    color: CHART_CONSTANTS.COLORS.TEXT,
                    font: {
                        size: CHART_CONSTANTS.DIMENSIONS.LEGEND_FONT_SIZE,
                        weight: 'bold'
                    }
                },
                grid: { display: false },
                ticks: {
                    color: CHART_CONSTANTS.COLORS.TEXT,
                    maxTicksLimit: 8,
                    callback: function(value) {
                        const date = new Date(value);
                        return new Intl.DateTimeFormat('en-US', {
                            timeZone: 'Europe/Riga',
                            hour: '2-digit',
                            minute: '2-digit',
                            hour12: false
                        }).format(date);
                    }
                }
            },
            y: {
                type: 'linear',
                display: true,
                position: 'left',
                title: {
                    display: true,
                    text: 'Temperature (°C) / Wind Speed (m/s)',
                    color: CHART_CONSTANTS.COLORS.TEXT
                },
                min: scaleRanges.combined.min,
                max: scaleRanges.combined.max,
                grid: { color: CHART_CONSTANTS.COLORS.GRID },
                ticks: { color: CHART_CONSTANTS.COLORS.TEXT }
            },
            y2: {
                type: 'linear',
                display: false, // Hide the axis but keep the scale
                position: 'left',
                min: scaleRanges.precipitation.min,
                max: scaleRanges.precipitation.max,
                grid: { drawOnChartArea: false }
            }
        };
    }

    /**
     * Calculate appropriate scale range with padding
     */
    static calculateScaleRange(min, max, secondaryMin = null, secondaryMax = null) {
        if (min === max) {
            return { min: min - 1, max: max + 1 };
        }

        const range = max - min;
        const padding = range * 0.1;

        let finalMin = min - padding;
        let finalMax = max + padding;

        // Consider secondary axis if provided
        if (secondaryMin !== null && secondaryMax !== null) {
            const tempRange = finalMax - finalMin;
            const windRange = secondaryMax - secondaryMin;
            const ratio = tempRange / windRange;

            if (ratio > 2) {
                finalMax = finalMin + (windRange * 2);
            } else if (ratio < 0.5) {
                finalMin = finalMax - (windRange * 2);
            }
        }

        return { min: finalMin, max: finalMax };
    }

    /**
     * Create reset button element
     */
    static createChartElements(containerId) {
        const canvas = document.getElementById(containerId);
        if (!canvas) return;

        const container = canvas.parentElement;
        if (!container) return;

        // Remove existing elements
        const existingElements = container.querySelectorAll('.weather-timezone-title, .weather-timezone-top, .weather-timezone-bottom, .weather-timezone-local-title, .weather-reset-btn, .weather-legend');
        existingElements.forEach(el => el.remove());

        // Create and setup reset button
        const resetBtn = WeatherHistoryChart.createResetButton(containerId);
        container.appendChild(resetBtn);

        // Setup container
        container.style.position = 'relative';

        // Position button after chart renders
        setTimeout(() => {
            WeatherHistoryChart.positionResetButton(resetBtn, canvas, container);
        }, CHART_CONSTANTS.TIMING.BUTTON_POSITION_DELAY);

        // Add resize listener with cleanup
        const resizeHandler = () => {
            WeatherHistoryChart.positionResetButton(resetBtn, canvas, container);
        };

        // Store handler for cleanup
        WeatherHistoryChart.resizeHandlers.set(containerId, resizeHandler);
        window.addEventListener('resize', resizeHandler);
    }

    /**
     * Create reset button element
     */
    static createResetButton(containerId) {
        const resetBtn = document.createElement('button');
        resetBtn.className = 'weather-reset-btn';
        resetBtn.textContent = 'Reset Zoom';
        resetBtn.style.cssText = `
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background-color: ${CHART_CONSTANTS.COLORS.RESET_BUTTON};
            color: white;
            border: none;
            padding: ${CHART_CONSTANTS.DIMENSIONS.BUTTON_PADDING};
            border-radius: 4px;
            font-size: ${CHART_CONSTANTS.DIMENSIONS.LEGEND_FONT_SIZE}px;
            font-family: Inter, system-ui, sans-serif;
            cursor: pointer;
            transition: background-color 0.2s;
            z-index: 10;
        `;

        // Add hover effects
        resetBtn.addEventListener('mouseenter', () => {
            resetBtn.style.backgroundColor = CHART_CONSTANTS.COLORS.RESET_BUTTON_HOVER;
        });

        resetBtn.addEventListener('mouseleave', () => {
            resetBtn.style.backgroundColor = CHART_CONSTANTS.COLORS.RESET_BUTTON;
        });

        resetBtn.addEventListener('click', () => {
            const chartInstance = Chart.getChart(containerId);
            if (chartInstance) {
                chartInstance.resetZoom();
            }
        });

        return resetBtn;
    }

    /**
     * Position reset button relative to canvas
     */
    static positionResetButton(resetBtn, canvas, container) {
        const canvasRect = canvas.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        const canvasBottomOffset = (canvasRect.bottom - containerRect.top) + 15;
        resetBtn.style.top = `${canvasBottomOffset}px`;
        resetBtn.style.bottom = 'auto';
    }

    /**
     * Destroy a specific chart instance
     */
    static destroy(containerId) {
        const chart = WeatherHistoryChart.chartInstances.get(containerId);
        if (chart) {
            chart.destroy();
            WeatherHistoryChart.chartInstances.delete(containerId);
        }

        // Clean up resize handler
        const resizeHandler = WeatherHistoryChart.resizeHandlers.get(containerId);
        if (resizeHandler) {
            window.removeEventListener('resize', resizeHandler);
            WeatherHistoryChart.resizeHandlers.delete(containerId);
        }
    }

    /**
     * Destroy all chart instances
     */
    static destroyAll() {
        WeatherHistoryChart.chartInstances.forEach((chart) => {
            chart.destroy();
        });
        WeatherHistoryChart.chartInstances.clear();

        // Clean up all resize handlers
        WeatherHistoryChart.resizeHandlers.forEach((handler) => {
            window.removeEventListener('resize', handler);
        });
        WeatherHistoryChart.resizeHandlers.clear();
    }
}

/**
 * Weather History API Manager - Handles API calls and data management
 */
class WeatherHistoryAPI {
    /**
     * Fetch weather history data for a workorder
     * @param {number} workorderId - ID of the workorder
     * @returns {Promise<Object>} Weather history data or error
     */
    static async fetchWeatherHistory(workorderId) {
        try {
            const response = await fetch(`/workorders/weather-history/${workorderId}/`);
            const result = await response.json();

            if (result.success) {
                return { success: true, data: result.data };
            } else {
                return { success: false, error: result.error || 'Failed to load weather history data' };
            }
        } catch (error) {
            return { success: false, error: 'Network error: ' + error.message };
        }
    }
}

/**
 * Weather History Component Manager - Handles Alpine.js component logic
 */
class WeatherHistoryComponent {
    /**
     * Create Alpine.js data object for weather history component
     * @param {number} workorderId - ID of the workorder
     * @returns {Object} Alpine.js data object
     */
    static createAlpineData(workorderId) {
        return {
            workorderId: workorderId,
            weatherData: null,
            loading: false,
            error: null,

            init() {
                // Component initialization - weather history will be loaded via x-init watcher
            },

            async loadWeatherHistory() {
                if (!this.workorderId) {
                    this.error = 'No workorder ID provided';
                    return;
                }

                // Don't load if we already have data or are currently loading
                if (this.weatherData || this.loading) {
                    return;
                }

                this.loading = true;
                this.error = null;

                const result = await WeatherHistoryAPI.fetchWeatherHistory(this.workorderId);

                if (result.success) {
                    this.weatherData = result.data;
                    this.$nextTick(() => {
                        // Add small delay to ensure Alpine.js x-show has fully updated DOM visibility
                        setTimeout(() => {
                            this.renderWeatherChart();
                        }, CHART_CONSTANTS.TIMING.RENDER_DELAY);
                    });
                } else {
                    this.error = result.error;
                }

                this.loading = false;
            },

            renderWeatherChart() {
                if (this.weatherData) {
                    // Simply render the chart without resize logic
                    WeatherHistoryChart.render('weather-history-chart', this.weatherData);
                }
            }
        };
    }
}

/**
 * Global function for Alpine.js component initialization
 * @param {number} workorderId - ID of the workorder
 * @returns {Object} Alpine.js data object
 */
function weatherHistoryComponent(workorderId) {
    return WeatherHistoryComponent.createAlpineData(workorderId);
}

// Export for global use
window.WeatherHistoryChart = WeatherHistoryChart;
window.WeatherHistoryAPI = WeatherHistoryAPI;
